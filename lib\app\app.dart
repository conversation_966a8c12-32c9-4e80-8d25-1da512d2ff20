import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/blocs/language_bloc/language_bloc.dart';
import 'package:rozana/core/connectivity/bloc/connectivity_bloc.dart';
import 'package:rozana/core/themes/app_theme.dart';
import 'package:rozana/core/themes/theme_bloc/theme_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_event.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/features/location/bloc/location_permission_bloc/location_permission_bloc.dart';
import 'package:rozana/features/location/bloc/location_permission_bloc/location_permission_event.dart';
import 'package:rozana/l10n/app_localizations.dart';
import 'package:rozana/web-view/bloc/bloc/web_view_bloc.dart';
import 'package:rozana/web-view/presentation/screens/web_screen.dart';
import '../core/connectivity/bloc/connectivity_state.dart';
import '../core/dependency_injection/di_container.dart';
import '../core/utils/constants.dart';
import '../features/location/bloc/location_permission_bloc/location_permission_state.dart';
import '../features/location/presentation/widgets/location_disabled_status_handler.dart';
import '../routes/app_router.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late GoRouter _router;

  @override
  void initState() {
    super.initState();
    // Initialize GoRouter with the Bloc for redirection logic
    _router = AppRouter.createRouter(context);
    // Initialize BLoCs for mobile view to avoid loading delays
    _initializeMobileBloCs();
  }

  Future<void> _initializeMobileBloCs() async {
    // Initialize BLoCs for mobile view
    final themeBloc = getIt<ThemeBloc>();
    final locationBloc = getIt<LocationBloc>();
    final cartBloc = getIt<CartBloc>();

    themeBloc.add(const ThemeEvent.init());

    cartBloc.add(const CartEvent.init());

    // Initialize location but don't wait for it to complete
    // This prevents the loading state from blocking the UI
    locationBloc.add(const LocationEvent.started());
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: getIt<WebViewBloc>(),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Check screen width and update bloc state
          final webViewBloc = context.read<WebViewBloc>();
          webViewBloc.checkScreenWidth(constraints.maxWidth);

          return BlocBuilder<WebViewBloc, WebViewState>(
            builder: (context, state) {
              return state.maybeWhen(
                webViewMode: () => MaterialApp(
                  home: const WebScreen(),
                  debugShowCheckedModeBanner: false,
                  title: AppConstants.appName,
                  scaffoldMessengerKey: scaffoldMessengerKey,
                  theme: AppTheme.lightTheme,
                  darkTheme: AppTheme.darkTheme,
                ),
                nativeViewMode: () => CoreAppWidget(router: _router),
                orElse: () {
                  // Default behavior based on platform and screen width for initial state
                  // Only show web view on web platform, never on mobile
                  final isWebViewMode = kIsWeb && constraints.maxWidth > 600;
                  if (isWebViewMode) {
                    return MaterialApp(
                      home: const WebScreen(),
                      debugShowCheckedModeBanner: false,
                      title: AppConstants.appName,
                      scaffoldMessengerKey: scaffoldMessengerKey,
                      theme: AppTheme.lightTheme,
                      darkTheme: AppTheme.darkTheme,
                    );
                  }
                  return CoreAppWidget(router: _router);
                },
              );
            },
          );
        },
      ),
    );
  }
}

/// Core app widget that contains the main business logic and UI components
/// This widget can be reused in both native mobile app and web view mobile frame
class CoreAppWidget extends StatelessWidget {
  final GoRouter router;
  final bool isWebFrame;

  const CoreAppWidget({
    super.key,
    required this.router,
    this.isWebFrame = false,
  });

  @override
  Widget build(BuildContext context) {
    final newMediaQueryData = MediaQuery.of(context).copyWith(
      textScaler: MediaQuery.of(context)
          .textScaler
          .clamp(minScaleFactor: 1.0, maxScaleFactor: 1.2),
    );
    return MultiBlocProvider(
      providers: [
        BlocProvider.value(value: getIt<ThemeBloc>()),
        BlocProvider.value(value: getIt<LanguageBloc>()),
        BlocProvider.value(value: getIt<LocationBloc>()),
        BlocProvider.value(value: getIt<CartBloc>()),
        BlocProvider.value(value: getIt<WebViewBloc>()),
        BlocProvider.value(value: getIt<ConnectivityBloc>()),
        BlocProvider.value(
            value: getIt<LocationPermissionBloc>()
              ..add(LocationPermissionEvent.checkPermissions())),
        // Other global BLoCs can be added here
      ],
      child: MultiBlocListener(
        listeners: [
          BlocListener<AppBloc, AppState>(listenWhen: (previous, current) {
            bool? previousValue =
                (previous.mapOrNull(loaded: (value) => value.isAuthenticated));
            bool? currentValue =
                (current.mapOrNull(loaded: (value) => value.isAuthenticated));

            return ((previousValue != null) && (currentValue != null)) &&
                (previousValue != currentValue);
          }, listener: (context, state) {
            state.mapOrNull(loaded: (value) {
              // Removed automatic profile navigation - let LoginScreen handle navigation
              if (!value.isAuthenticated) {
                if (navigatorKey.currentState?.context != null) {
                  navigatorKey.currentState!.context.go(RouteNames.login);
                }
              }
            });
          }),
          BlocListener<ConnectivityBloc, ConnectivityState>(
              listener: (context, state) {
            // // Get the current location from GoRouter to prevent pushing the screen multiple times
            String currentLocation = router.state.uri.toString();

            state.when(
              initial: () {
                // Do nothing on initial state
              },
              checking: () {
                // Do nothing specific when checking, UI handles loading indicator
              },
              disconnected: () {
                if (currentLocation != RouteNames.noNetworkScreen) {
                  if (navigatorKey.currentState?.context != null) {
                    navigatorKey.currentState?.context
                        .push(RouteNames.noNetworkScreen);
                  }
                }
              },
              connected: () {
                if (currentLocation == RouteNames.noNetworkScreen) {
                  if (navigatorKey.currentState?.context != null) {
                    navigatorKey.currentState?.context.pop();
                  }
                }
              },
            );
          }),
          BlocListener<LocationPermissionBloc, LocationPermissionState>(
            listenWhen: (previous, current) =>
                (previous.runtimeType != current.runtimeType),
            listener: (context, state) {
              state.whenOrNull(
                grantedAndEnabled: () {
                  if (LocationPermissionBloc.isBottomSheetShowing) {
                    if (navigatorKey.currentState?.context != null) {
                      navigatorKey.currentState?.context.pop();
                    }
                  }
                  getIt<LocationBloc>().add(LocationEvent.refreshLocation());
                },
                permissionDenied: () {
                  if (navigatorKey.currentState?.context != null) {
                    showLocationIssueBottomSheet(
                        navigatorKey.currentState!.context,
                        LocationIssueType.permissionDenied);
                  }
                },
                // Show bottom sheet for permission permanently denied (needs app settings)
                permissionPermanentlyDenied: () {
                  if (navigatorKey.currentState?.context != null) {
                    showLocationIssueBottomSheet(
                        navigatorKey.currentState!.context,
                        LocationIssueType.permissionPermanentlyDenied);
                  }
                },
                // Show bottom sheet for location service disabled
                serviceDisabled: () {
                  LocationPermissionBloc.isFirstCheck = false;
                  if (navigatorKey.currentState?.context != null) {
                    showLocationIssueBottomSheet(
                        navigatorKey.currentState!.context,
                        LocationIssueType.serviceDisabled);
                  }
                },
                // Handle error, e.g., show a SnackBar
                error: (message) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error: $message')),
                  );
                },
              );
            },
          )
        ],
        child: BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, themeState) {
            return BlocBuilder<LanguageBloc, LanguageState>(
              builder: (context, languageState) {
                final locale = languageState.mapOrNull(
                      loaded: (state) => state.locale,
                    ) ??
                    const Locale('en'); // Default to English

                return MediaQuery(
                  data: newMediaQueryData,
                  child: MaterialApp.router(
                    debugShowCheckedModeBanner: false,
                    title: AppConstants.appName,
                    scaffoldMessengerKey:
                        isWebFrame ? null : scaffoldMessengerKey,
                    theme: AppTheme.lightTheme,
                    darkTheme: AppTheme.darkTheme,
                    themeMode: themeState.themeMode,
                    routerConfig: router,
                    // Localization configuration
                    localizationsDelegates: const [
                      AppLocalizations.delegate,
                      GlobalMaterialLocalizations.delegate,
                      GlobalWidgetsLocalizations.delegate,
                      GlobalCupertinoLocalizations.delegate,
                    ],
                    supportedLocales: const [
                      Locale('en'), // English (Default)
                      Locale('hi'), // Hindi
                    ],
                    locale: locale, // Dynamic locale from LanguageBloc
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
