import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/routes/app_router.dart';
import 'package:rozana/features/products/presentation/widgets/product_variant_bottom_sheet.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/widgets/custom_text.dart';
import 'package:rozana/widgets/shimmer_widgets.dart';

import '../../../../core/services/appflyer_services/appflyer_events.dart';
import '../../../../core/themes/color_schemes.dart';
import '../../../../data/models/cart_item_model.dart';
import '../../../../domain/entities/product_entity.dart';
import '../../../cart/bloc/cart_bloc.dart';
import '../../../cart/bloc/cart_event.dart';
import '../../../cart/bloc/cart_state.dart';
import '../../../cart/utils/cart_utils.dart';

class DiscountedProductCard extends StatelessWidget {
  final ProductEntity? product;
  final bool isLoading;
  final double? height;
  final bool staggeredLoading;
  final int staggerIndex;
  final EdgeInsets? imagePadding;
  final EdgeInsets? textPadding;

  const DiscountedProductCard({
    super.key,
    required this.product,
    required this.isLoading,
    this.height,
    this.staggeredLoading = false,
    this.staggerIndex = 0,
    this.imagePadding,
    this.textPadding,
  });

  @override
  Widget build(BuildContext context) {
    List<ProductEntity>? items = product?.variants;
    ProductEntity? selectedProduct = product;
    return isLoading
        ? ProductCardShimmer()
        : BlocBuilder<CartBloc, CartState>(
            buildWhen: (previous, current) {
              bool isQuantityChanged = items?.any(
                    (element) {
                      final prevQuantity = CartUtils.getItemQuantity(
                          element.id, element.skuID ?? '', previous.cart);
                      final currQuantity = CartUtils.getItemQuantity(
                          element.id, element.skuID ?? '', current.cart);
                      return prevQuantity != currQuantity;
                    },
                  ) ??
                  false;
              return ((product?.totalVariants ?? 0) > 1) && (isQuantityChanged);
            },
            builder: (context, state) {
              List<ProductEntity> products = [];

              products.addAll(items ?? []);

              products.sort((a, b) {
                final quantityA =
                    CartUtils.getItemQuantity(a.id, a.skuID ?? '', state.cart);
                final quantityB =
                    CartUtils.getItemQuantity(b.id, b.skuID ?? '', state.cart);
                return quantityB.compareTo(quantityA);
              });

              if (products.isNotEmpty) {
                selectedProduct = products[0].copyWith(
                    // totalVariants: product?.totalVariants,
                    // variants: product?.variants
                    );
              }

              return GestureDetector(
                onTap: () async {
                  // showProductOptions(context);

                  String sku = selectedProduct?.parentSku ??
                      selectedProduct?.skuID?.split('-').first ??
                      '';
                  String location = GoRouterState.of(context).uri.toString();
                  if (location.contains(RouteNames.productDetail)) {
                    context.pushReplacementNamed(
                      RouteNames.productDetail,
                      pathParameters: {'id': sku},
                      extra: {
                        'sku': sku,
                        'variant_name': selectedProduct?.skuID?.split('-').last,
                      },
                    );
                  } else {
                    context.pushNamed(
                      RouteNames.productDetail,
                      pathParameters: {'id': sku},
                      extra: {
                        'sku': sku,
                        'variant_name': selectedProduct?.skuID?.split('-').last,
                      },
                    );
                  }

                  // Log product view event to AppsFlyer
                  await AppsFlyerEvents.productView(
                      sku: selectedProduct?.skuID,
                      productName: selectedProduct?.name,
                      price: selectedProduct?.price,
                      category: selectedProduct?.category);
                },
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AspectRatio(
                      aspectRatio: 1,
                      child: Stack(
                        alignment: Alignment.bottomRight,
                        children: [
                          Container(
                            margin: EdgeInsets.only(right: 5, bottom: 5),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20.0),
                              border: Border.all(
                                  color: AppColors.neutral200, width: 1),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(20.0),
                              child: Padding(
                                padding: const EdgeInsets.fromLTRB(8, 8, 8, 8),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(10.0),
                                  child: AspectRatio(
                                    aspectRatio: 1,
                                    child: CustomImage(
                                      imageUrl: selectedProduct?.imageUrl,
                                      width: double.infinity,
                                      fit: BoxFit.cover,
                                      height: double.infinity,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Row(
                            children: [
                              Spacer(flex: 5),
                              Flexible(
                                flex: 6,
                                child: QuantityTogglerWidget(
                                  height: 38,
                                  product: product,
                                  selectedProduct: selectedProduct,
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: textPadding ??
                            const EdgeInsets.fromLTRB(0, 2, 8, 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ConstrainedBox(
                              constraints: BoxConstraints(minHeight: 32),
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child: CustomText(
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  selectedProduct?.name ?? '--',
                                  fontSize: 12,
                                  fontWeight: FontWeight.w700,
                                  textHeight: 1.4,
                                ),
                              ),
                            ),
                            SizedBox(height: 2),
                            Flexible(
                              child: Row(
                                children: [
                                  CustomText(
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    '₹${(selectedProduct?.price ?? 0).toStringAsFixed(0)}',
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    textHeight: 1.4,
                                    color: AppColors.neutral700,
                                  ),
                                  SizedBox(width: 6),
                                  IntrinsicWidth(
                                    child: Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        CustomText(
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                          '₹${(selectedProduct?.originalPrice ?? 0).toStringAsFixed(0)}',
                                          fontSize: 12,
                                          fontWeight: FontWeight.bold,
                                          textHeight: 1.4,
                                          color: AppColors.neutral400,
                                        ),
                                        Transform.rotate(
                                          angle: 2.9,
                                          child: Container(
                                            height: 1,
                                            width: double.infinity,
                                            color: AppColors.neutral400,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Flexible(
                              child: Row(
                                children: [
                                  Visibility(
                                      visible: false,
                                      child: Padding(
                                        padding:
                                            const EdgeInsets.only(right: 4),
                                        child: Image.asset(
                                          'assets/new/icons/veg_symbol.png',
                                          width: 16,
                                          height: 16,
                                        ),
                                      )),
                                  if (selectedProduct?.variantName != null &&
                                      selectedProduct!.variantName!.isNotEmpty)
                                    Flexible(
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 4, vertical: 2),
                                        decoration: BoxDecoration(
                                          color: Color(0xFFf7f7f7),
                                        ),
                                        child: CustomText(
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          ' ${selectedProduct!.variantName}',
                                          fontSize: 10,
                                          color: AppColors.neutral500,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          );
  }
}

class QuantityTogglerWidget extends StatelessWidget {
  const QuantityTogglerWidget({
    super.key,
    required this.height,
    this.product,
    this.showVariantsBottomSheet = true,
    this.selectedProduct,
  });
  final double height;
  final ProductEntity? product;
  final ProductEntity? selectedProduct;
  final bool showVariantsBottomSheet;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        String productId = selectedProduct?.id ?? '';
        final quantity = CartUtils.getItemQuantity(
            productId, selectedProduct?.skuID ?? '', state.cart);

        return Align(
          alignment: Alignment.bottomRight,
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: quantity > 0 ? 90 : 70),
            child: Stack(
              children: [
                Container(
                    height: height,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(height / 3),
                      gradient: quantity > 0
                          ? RadialGradient(
                              colors: [
                                Color(0xFFBEABD3),
                                Color(0xFF7D56A6),
                              ],
                              center: Alignment(0, -(height * 0.046)),
                              radius: 1,
                              transform: _EllipticalGradientTransform(
                                widthFactor:
                                    height * 0.023, // Stretch horizontally
                                heightFactor:
                                    height * 0.012, // Compress vertically
                              ),
                            )
                          : null,
                      color: quantity > 0 ? null : Colors.white,
                      border: quantity > 0
                          ? null
                          : Border.all(
                              color: AppColors.primary500,
                            ),
                    ),
                    child: quantity > 0
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Minus button
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 4, vertical: 3),
                                  child: Image.asset(
                                    'assets/new/icons/remove_from_cart.png',
                                    width: 12,
                                    height: 12,
                                  ),
                                ),
                              ),

                              // Quantity display
                              Flexible(
                                child: AbsorbPointer(
                                  absorbing: true,
                                  child: FittedBox(
                                    child: CustomText(
                                      '$quantity',
                                      color: AppColors.surface,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ),

                              // Plus button
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 4, vertical: 3),
                                  child: Image.asset(
                                    'assets/new/icons/add_to_cart.png',
                                    width: 12,
                                    height: 12,
                                  ),
                                ),
                              ),
                            ],
                          )
                        : Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Expanded(
                                child: Center(
                                  child: const CustomText(
                                    'ADD',
                                    color: AppColors.primary500,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              if ((product?.totalVariants ?? 0) > 1)
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 2, vertical: 1),
                                  decoration: BoxDecoration(
                                      color: AppColors.primary100,
                                      borderRadius: BorderRadius.only(
                                          bottomLeft:
                                              Radius.circular(height / 3),
                                          bottomRight:
                                              Radius.circular(height / 3))),
                                  child: Center(
                                    child: CustomText(
                                      "${product?.totalVariants ?? 0} options",
                                      color: AppColors.primary500,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 8,
                                    ),
                                  ),
                                )
                            ],
                          )),
                Row(
                  children: [
                    Expanded(
                        child: InkWell(
                      splashColor: Colors.transparent,
                      onTap: () async {
                        HapticFeedback.lightImpact();

                        // Check if product has multiple variants
                        if ((product?.totalVariants ?? 0) > 1 &&
                            (product?.variants != null) &&
                            (showVariantsBottomSheet)) {
                          _showProductVariantBottomSheet(context, product!);
                          return;
                        }

                        if (quantity > 0) {
                          final cartItemId = CartUtils.getCartItemId(
                              productId,
                              selectedProduct?.childSku ??
                                  (selectedProduct?.skuID ?? ''),
                              state.cart);

                          if (cartItemId != null) {
                            if (quantity > 1) {
                              context.read<CartBloc>().add(
                                  CartEvent.updateQuantity(
                                      cartItemId,
                                      selectedProduct?.childSku ??
                                          (selectedProduct?.skuID ?? ''),
                                      (quantity - 1).toInt()));
                            } else if (quantity == 1) {
                              context.read<CartBloc>().add(CartEvent.removeItem(
                                    cartItemId,
                                    selectedProduct?.childSku ??
                                        (selectedProduct?.skuID ?? ''),
                                  ));
                            }
                          }
                        } else {
                          String screen = showVariantsBottomSheet
                              ? GoRouterState.of(context).uri.toString()
                              : "variant_bottom_sheet";
                          context.read<CartBloc>().add(CartEvent.addItem(
                              item: CartItemModel(
                                productId: productId,
                                name: selectedProduct?.name,
                                price: selectedProduct?.originalPrice,
                                imageUrl: selectedProduct?.imageUrl,
                                quantity: 1,
                                facilityId: selectedProduct?.facilityId,
                                facilityName: selectedProduct?.facilityName,
                                unit: 'item',
                                discountedPrice: selectedProduct?.price,
                                skuID: (selectedProduct?.childSku ??
                                    selectedProduct?.skuID),
                                availableQuantity:
                                    selectedProduct?.availableQty,
                                maxQuantity: selectedProduct?.maxLimit,
                                tax: selectedProduct?.tax,
                                cgst: selectedProduct?.cgst,
                                sgst: selectedProduct?.sgst,
                              ),
                              screen: screen));
                        }
                      },
                      child: SizedBox(
                        height: height,
                      ),
                    )),
                    quantity > 0
                        ? Expanded(
                            child: InkWell(
                            splashColor: Colors.transparent,
                            onTap: () async {
                              HapticFeedback.lightImpact();
                              // Check if product has multiple variants
                              if ((product?.totalVariants ?? 0) > 1 &&
                                  (product?.variants != null) &&
                                  (showVariantsBottomSheet)) {
                                _showProductVariantBottomSheet(
                                    context, product!);
                                return;
                              }
                              final cartItemId = CartUtils.getCartItemId(
                                  productId,
                                  selectedProduct?.childSku ??
                                      (selectedProduct?.skuID ?? ''),
                                  state.cart);
                              if (cartItemId != null) {
                                String screen = showVariantsBottomSheet
                                    ? GoRouterState.of(context).uri.toString()
                                    : "variant_bottom_sheet";
                                context
                                    .read<CartBloc>()
                                    .add(CartEvent.updateQuantity(
                                      cartItemId,
                                      selectedProduct?.childSku ??
                                          (selectedProduct?.skuID ?? ''),
                                      (quantity + 1).toInt(),
                                      screen: screen,
                                    ));
                              }

                              // widget.onAddToCart?.call();
                            },
                            child: SizedBox(
                              height: height,
                            ),
                          ))
                        : SizedBox(),
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }

  /// Show product variant bottom sheet for products with multiple variants
  void _showProductVariantBottomSheet(
      BuildContext context, ProductEntity product) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useRootNavigator: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (_) {
        return ProductVariantBottomSheet(
          productTitle: product.name,
          variants: product.variants ?? [],
        );
      },
    );
  }
}

// Custom GradientTransform to apply elliptical scaling
class _EllipticalGradientTransform extends GradientTransform {
  final double widthFactor;
  final double heightFactor;

  const _EllipticalGradientTransform({
    required this.widthFactor,
    required this.heightFactor,
  });

  @override
  Matrix4? transform(Rect bounds, {TextDirection? textDirection}) {
    // Calculate the center of the bounds
    final double centerX = bounds.center.dx;
    final double centerY = bounds.center.dy;

    // Create a translation matrix to move the center to the origin (0,0)
    final Matrix4 matrix = Matrix4.identity()
      ..translate(centerX, centerY)
      ..scale(widthFactor, heightFactor) // Apply the non-uniform scale
      ..translate(-centerX, -centerY); // Move back to original center

    return matrix;
  }
}

class ProductCardShimmer extends StatelessWidget {
  const ProductCardShimmer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
            child: CustomShimmer(
          radius: 20,
          baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
          highlightColor: Color(0xFFD8D5EA),
        )),
        SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.fromLTRB(8, 4, 8, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 10),
                child: CustomShimmer(
                  height: 16,
                  width: double.infinity,
                  baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
                  highlightColor: Color(0xFFD8D5EA),
                ),
              ),
              SizedBox(height: 8),
              CustomShimmer(
                height: 16,
                width: 100,
                baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
                highlightColor: Color(0xFFD8D5EA),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  CustomShimmer(
                    height: 16,
                    width: 16,
                    baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
                    highlightColor: Color(0xFFD8D5EA),
                  ),
                  SizedBox(width: 4),
                  CustomShimmer(
                    height: 16,
                    width: 90,
                    baseColor: Color(0xFFB2A9E6).withValues(alpha: 0.3),
                    highlightColor: Color(0xFFD8D5EA),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
