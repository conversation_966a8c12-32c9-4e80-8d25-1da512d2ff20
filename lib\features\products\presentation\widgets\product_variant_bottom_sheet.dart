import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_image.dart';
import 'package:rozana/domain/entities/product_entity.dart';
import 'package:rozana/features/products/presentation/widgets/product_card.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';

class ProductVariantBottomSheet extends StatelessWidget {
  final String productTitle;
  final List<ProductEntity> variants;

  const ProductVariantBottomSheet({
    super.key,
    required this.productTitle,
    required this.variants,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SafeArea(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.75,
          ),
          child: NotificationListener<UserScrollNotification>(
            onNotification: (notification) {
              return false;
            },
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(context),
                Flexible(
                  child: ListView.builder(
                    shrinkWrap: true,
                    primary: true,
                    physics: ClampingScrollPhysics(),
                    padding: const EdgeInsets.only(bottom: 20, top: 20),
                    itemCount: variants.length,
                    itemBuilder: (context, index) => _buildVariantItem(index),
                  ),
                ),
                _buildDoneButton(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: const BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              productTitle,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.neutral800,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
          const SizedBox(width: 12),
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: const Icon(Icons.close, color: AppColors.neutral800),
          ),
        ],
      ),
    );
  }

  Widget _buildVariantItem(int index) {
    final variant = variants[index];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          CustomImage(
            imageUrl: variant.imageUrl ?? '',
            width: 50,
            height: 50,
            fit: BoxFit.cover,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(variant.variantName ?? variant.name,
                style: const TextStyle(fontSize: 16)),
          ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  "₹${variant.price.toStringAsFixed(0)}",
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 6),
                if (variant.originalPrice != null &&
                    variant.originalPrice! > variant.price)
                  Text(
                    "₹${variant.originalPrice!.toStringAsFixed(0)}",
                    style: const TextStyle(
                      fontSize: 12,
                      decoration: TextDecoration.lineThrough,
                      color: AppColors.neutral400,
                    ),
                  ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          SizedBox(
            width: 100,
            child: _BottomSheetQuantityToggler(
              height: 38,
              variant: variant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDoneButton(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.neutral800.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary500,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          minimumSize: const Size(double.infinity, 48),
        ),
        onPressed: () => Navigator.pop(context),
        child: const Text('Done',
            style: TextStyle(fontSize: 16, color: AppColors.neutral100)),
      ),
    );
  }
}
